<img src="https://res.cloudinary.com/bytefury/image/upload/v1574149856/Crater/craterframe.png">

## Introduction

Crater is an open-source web & mobile app that helps you track expenses, payments & create professional invoices & estimates.

Web Application is made using <PERSON><PERSON> & VueJS while the Mobile Apps are built using React Native.

# Table of Contents

1. [Documentation](#documentation)
2. [Download](#download)
3. [Mobile Apps](#mobile-apps)
4. [Discord](#discord)
5. [Roadmap](#roadmap)
6. [Credits](#credits)
7. [Help us translate](#translate)
8. [License](#license)

## Documentation

- [Installation Steps](https://docs.craterapp.com/installation.html)
- [User Guide](https://docs.craterapp.com/)
- [Developer Guide](https://docs.craterapp.com/developer-guide.html)
- [API Documentation](https://api-docs.craterapp.com)

## Download

- [Download Link](https://craterapp.com/downloads)

## Mobile Apps

- [Android](https://play.google.com/store/apps/details?id=com.craterapp.app)
- [IOS](https://apps.apple.com/app/id1489169767)
- [Source](https://github.com/bytefury/crater-mobile)

## Discord

Join the Crater discord server to discuss:
[Invite Link](https://discord.gg/nyTstm6)

## Roadmap

~~Here's a rough roadmap of things to come (not in any specific order):

- [x] Automatic Update
- [x] Email Configuration
- [x] Installation Wizard
- [x] Address Customisation & Default notes
- [x] Edit Email before Sending Invoice
- [x] Available as a docker image
- [x] Performance Improvements
- [x] Customer View page
- [x] Add and Use Custom Fields on Invoices & Estimates.
- [x] Multiple Companies
- [x] Recurring Invoices
- [x] Customer Portal
- [x] Accept Payments (Stripe Integration)
- [x] White Labeling (Easy Invoice, Email & Consumer Portal Theme customisation)
- [ ] Modules API
- [ ] Blockchain Integration
- [ ] Web 3.0 Accounting
- [ ] Vendors & Bills
- [ ] Inventory Management 
- [ ] Payment Reminders
- [ ] Improve Accessibility
- [ ] Debit & Credit Notes
- [ ] Time Tracking
- [ ] Full service Payroll


## Copyright

© 2022 Crater Invoice, Inc.

**Special thanks to:**

- [Birkhoff Lee](https://github.com/BirkhoffLee)
- [Akaunting](https://github.com/akaunting/akaunting)
- [MakerLab](https://github.com/MakerLab-Dev)
- [Sebastian Cretu](https://github.com/sebastiancretu)
- [Florian Gareis](https://github.com/TheZoker)

## Translate

Help us translate on https://crowdin.com/project/crater-invoice

**Thanks to Translation Contributors:**

- [Hassan A. Ba Abdullah (Arabic)](https://github.com/hsnapps)
- [Clément de Louvencourt (French)](https://github.com/PHClement)
- [Robin Delattre (French)](https://github.com/RobinDev)
- [René Loos (Dutch)](https://github.com/Loosie94)
- [Stefan Azarić (Serbian)](https://github.com/azaricstefan)
- [Emmanuel Lampe (German)](https://github.com/rexlManu)
- [edevrob (Latvian)](https://github.com/edevrob)

## License

Crater is released under the GNU AFFERO GENERAL PUBLIC LICENSE Version 3.
See [LICENSE](LICENSE) for details.
