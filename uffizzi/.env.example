APP_ENV=production
APP_KEY=base64:kgk/4DW1vEVy7aEvet5FPp5un6PIGe/so8H0mvoUtW0=
APP_DEBUG=true
APP_LOG_LEVEL=debug
APP_URL=http://crater.test

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=crater
DB_USERNAME=crater
DB_PASSWORD=crater

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_DRIVER=sync
SESSION_DRIVER=cookie
SESSION_LIFETIME=1440

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=
MAIL_PORT=
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=

PUSHER_APP_ID=
PUSHER_KEY=
PUSHER_SECRET=

SANCTUM_STATEFUL_DOMAINS=crater.test
SESSION_DOMAIN=crater.test

TRUSTED_PROXIES="*"

CRON_JOB_AUTH_TOKEN=""
